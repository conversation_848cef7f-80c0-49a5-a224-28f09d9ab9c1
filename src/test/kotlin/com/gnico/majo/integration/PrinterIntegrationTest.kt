package com.gnico.majo.integration

import com.gnico.majo.adapter.printer.EscPosPrinterAdapter
import com.gnico.majo.adapter.printer.PrinterConnectionException
import com.gnico.majo.application.domain.model.*
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Disabled
import java.math.BigDecimal
import java.time.LocalDateTime
import java.time.LocalDate

/**
 * Test de integración para verificar la funcionalidad de impresión
 * 
 * NOTA: Estos tests están deshabilitados por defecto porque requieren
 * una impresora física conectada. Para habilitarlos:
 * 1. Configurar PRINTER_IP y PRINTER_PORT en .env
 * 2. Conectar impresora térmica compatible ESC/POS
 * 3. Remover @Disabled de los tests que quieras ejecutar
 */
class PrinterIntegrationTest {
    
    private lateinit var printerAdapter: EscPosPrinterAdapter
    private lateinit var sale: Sale
    private lateinit var comprobante: Comprobante
    
    @BeforeEach
    fun setup() {
        printerAdapter = EscPosPrinterAdapter()
        
        // Crear datos de prueba
        val usuario = Usuario.create("testuser", "Test User", "Test User")
        // Ya no manejamos clientes - solo consumidor final
        
        val items = listOf(
            SaleItem.create(
                productoNombre = "Producto de Prueba 1",
                cantidad = BigDecimal("2.00"),
                precioUnitario = BigDecimal("121.00"), // Precio con IVA incluido
                tipoIva = TipoIva.IVA_21
            ),
            SaleItem.create(
                productoNombre = "Producto de Prueba 2 - Nombre Largo para Probar Formato",
                cantidad = BigDecimal("1.50"),
                precioUnitario = BigDecimal("66.30"), // Precio con IVA incluido
                tipoIva = TipoIva.IVA_10_5
            ),
            SaleItem.create(
                productoNombre = "Producto Exento",
                cantidad = BigDecimal("3.00"),
                precioUnitario = BigDecimal("50.00"),
                tipoIva = TipoIva.EXENTO
            )
        )
        
        sale = Sale.create(
            usuario = usuario,
            items = items,
            medioPago = "EFECTIVO",
            codigoTicketBalanza = "TKT-001"
        )
        
        comprobante = Comprobante.fromPersistence(
            id = Id(1),
            venta = Id(1),
            tipoComprobante = "FACTURA_B",
            puntoVenta = 1,
            numeroComprobante = 123,
            cae = "12345678901234",
            fechaEmision = LocalDateTime.now(),
            fechaVencimientoCae = LocalDate.now().plusDays(10),
            impTotal = BigDecimal("491.45"),
            impTotConc = BigDecimal.ZERO,
            impNeto = BigDecimal("406.16"),
            impIva = BigDecimal("85.29"),
            impTrib = BigDecimal.ZERO,
            monId = "PES",
            monCotiz = BigDecimal.ONE,
            estado = "APROBADO"
        )
    }
    
    @Test
    @Disabled("Requiere impresora física conectada - habilitar manualmente para pruebas")
    fun `should print fiscal ticket successfully`() {
        // Este test imprimirá un ticket fiscal real
        // Solo habilitar si tienes una impresora conectada
        printerAdapter.printTicket(comprobante, sale)

        // Si llegamos aquí sin excepción, la impresión fue exitosa
        println("✅ Ticket fiscal impreso exitosamente")
    }
    
    @Test
    @Disabled("Requiere impresora física conectada - habilitar manualmente para pruebas")
    fun `should print non-fiscal ticket successfully`() {
        // Este test imprimirá un ticket no fiscal real
        // Solo habilitar si tienes una impresora conectada
        printerAdapter.printTicket(null, sale)
        
        // Si llegamos aquí sin excepción, la impresión fue exitosa
        println("✅ Ticket no fiscal impreso exitosamente")
    }
    
    @Test
    fun `should handle printer connection gracefully when disabled`() {
        // Test que siempre se ejecuta - verifica comportamiento con impresión deshabilitada
        // Este test usa el adaptador normal que lee de .env
        // Si PRINTER_ENABLED=false en .env, no debería intentar imprimir

        try {
            printerAdapter.printTicket(comprobante, sale)
            println("✅ Impresión manejada correctamente (habilitada o deshabilitada)")
        } catch (e: PrinterConnectionException) {
            println("✅ Error de conexión manejado correctamente: ${e.message}")
        } catch (e: Exception) {
            println("✅ Excepción manejada: ${e.message}")
        }
    }

    @Test
    @Disabled("Puede imprimir si hay impresora conectada - habilitar manualmente para pruebas")
    fun `should handle printer connection timeout gracefully`() {
        // Este test verifica que se maneja correctamente la falta de conexión con timeout
        // NOTA: Si hay una impresora conectada, este test imprimirá un ticket

        try {
            printerAdapter.printTicket(comprobante, sale)
            println("✅ Impresión completada o manejada correctamente")
        } catch (e: PrinterConnectionException) {
            println("✅ Error de conexión manejado correctamente: ${e.message}")
            // Verificar que el mensaje incluye información sobre timeout
            assert(e.message?.contains("timeout", ignoreCase = true) == true ||
                   e.message?.contains("conexión", ignoreCase = true) == true) {
                "El mensaje de error debería indicar problema de conexión o timeout"
            }
        } catch (e: Exception) {
            println("✅ Excepción manejada: ${e.message}")
        }
    }
    
    @Test
    @Disabled("Solo para demostración - habilitar manualmente")
    fun `demonstration - print multiple ticket types`() {
        println("=== DEMOSTRACIÓN DE IMPRESIÓN ===")

        try {
            println("1. Imprimiendo ticket no fiscal...")
            printerAdapter.printTicket(null, sale)
            Thread.sleep(2000) // Pausa entre impresiones

            println("2. Imprimiendo ticket fiscal...")
            printerAdapter.printTicket(comprobante, sale)

            println("✅ Demostración completada exitosamente")

        } catch (e: PrinterConnectionException) {
            println("❌ No se pudo conectar con la impresora: ${e.message}")
            println("💡 Verificar configuración de PRINTER_IP y PRINTER_PORT en .env")
        } catch (e: Exception) {
            println("❌ Error inesperado: ${e.message}")
        }
    }

    @Test
    @Disabled("Requiere impresora física - habilitar para probar nuevos estilos")
    fun `test new styled tickets with physical printer`() {
        println("=== PRUEBA DE TICKETS CON ESTILOS ESC/POS ===")

        try {
            println("🎨 Imprimiendo ticket fiscal con estilos...")
            printerAdapter.printTicket(comprobante, sale)
            Thread.sleep(3000) // Pausa para ver el resultado

            println("🎨 Imprimiendo ticket no fiscal con estilos...")
            printerAdapter.printTicket(null, sale)
            Thread.sleep(3000)

            // Crear venta para consumidor final (todas las ventas son así ahora)
            val saleWithoutClient = Sale.create(
                usuario = sale.usuario,
                items = sale.items,
                medioPago = "TARJETA_CREDITO"
            )

            println("🎨 Imprimiendo ticket para consumidor final...")
            printerAdapter.printTicket(comprobante, saleWithoutClient)

            println("✅ Prueba de estilos completada exitosamente")
            println("📋 Verificar en los tickets impresos:")
            println("   • Nombre de empresa en tamaño doble")
            println("   • Información fiscal en negrita")
            println("   • IVA subrayado")
            println("   • Tipo de comprobante destacado")
            println("   • Totales en negrita")
            println("   • Separadores con líneas punteadas")

        } catch (e: PrinterConnectionException) {
            println("❌ No se pudo conectar con la impresora: ${e.message}")
            println("💡 Verificar configuración de PRINTER_IP y PRINTER_PORT en .env")
        } catch (e: Exception) {
            println("❌ Error inesperado: ${e.message}")
        }
    }
    
    /**
     * Test para verificar el formato con estilos ESC/POS sin imprimir físicamente
     * Este test siempre se ejecuta y muestra el contenido que se enviaría a la impresora
     */
    @Test
    fun `should generate correct styled ticket content format`() {
        // Crear un formatter para capturar el contenido sin imprimir
        val config = com.gnico.majo.infrastructure.config.PrinterConfiguration.fromEnvironment()
        val formatter = com.gnico.majo.infrastructure.printer.StyledTicketFormatter(config)

        // Test ticket fiscal con estilos
        println("=== CONTENIDO TICKET FISCAL CON ESTILOS ESC/POS ===")
        val fiscalOutput = java.io.ByteArrayOutputStream()
        val fiscalEscpos = com.github.anastaciocintra.escpos.EscPos(fiscalOutput)
        formatter.formatFiscalTicket(fiscalEscpos, comprobante, sale)

        val fiscalContent = convertEscPosToReadable(fiscalOutput.toByteArray())
        println(fiscalContent)
        println("====================================================")

        // Test ticket no fiscal con estilos
        println("=== CONTENIDO TICKET NO FISCAL CON ESTILOS ESC/POS ===")
        val nonFiscalOutput = java.io.ByteArrayOutputStream()
        val nonFiscalEscpos = com.github.anastaciocintra.escpos.EscPos(nonFiscalOutput)
        formatter.formatNonFiscalTicket(nonFiscalEscpos, sale)

        val nonFiscalContent = convertEscPosToReadable(nonFiscalOutput.toByteArray())
        println(nonFiscalContent)
        println("=======================================================")

        // Solo verificar que se genera contenido
        assert(fiscalContent.isNotBlank()) { "El ticket fiscal debe generar contenido" }
        assert(nonFiscalContent.isNotBlank()) { "El ticket no fiscal debe generar contenido" }
        assert(!nonFiscalContent.contains("CAE:")) { "El ticket no fiscal no debe contener CAE" }

        println("✅ Formato de tickets con estilos verificado correctamente")
    }

    /**
     * Convierte comandos ESC/POS a texto legible para mostrar
     */
    private fun convertEscPosToReadable(rawOutput: ByteArray): String {
        return String(rawOutput, Charsets.UTF_8)
            .replace("\u001B", "[ESC]")
            .replace("\u001D", "[GS]")
            .replace("\u0000", "[NULL]")
            .replace("\u0001", "[1]")
            .replace("\u0011", "[17]")
            .replace("\r", "")
    }

    /**
     * Test para verificar el nuevo formato con estilos ESC/POS
     * Muestra cómo se verían los comandos ESC/POS en el ticket
     */
    @Test
    fun `should generate styled ticket content with ESC POS commands`() {
        val config = com.gnico.majo.infrastructure.config.PrinterConfiguration.fromEnvironment()
        val styledFormatter = com.gnico.majo.infrastructure.printer.StyledTicketFormatter(config)

        // Crear un mock de EscPos para capturar los comandos
        val capturedCommands = mutableListOf<String>()
        val mockEscPos = object : com.github.anastaciocintra.escpos.EscPos(java.io.ByteArrayOutputStream()) {
            override fun writeLF(text: String): com.github.anastaciocintra.escpos.EscPos {
                capturedCommands.add(text)
                return super.writeLF(text)
            }
        }

        println("=== TICKET FISCAL CON ESTILOS ESC/POS ===")
        styledFormatter.formatFiscalTicket(mockEscPos, comprobante, sale)

        capturedCommands.forEach { command ->
            // Mostrar comandos ESC/POS de forma legible
            val displayCommand = command
                .replace("\u001B", "[ESC]")
                .replace("\u001D", "[GS]")
                .replace("\u0001", "[ON]")
                .replace("\u0000", "[OFF]")
            println(displayCommand)
        }
        println("==========================================")

        // Verificar que se aplicaron estilos
        val hasEscCommands = capturedCommands.any { it.contains("\u001B") }
        val hasGsCommands = capturedCommands.any { it.contains("\u001D") }

        assert(hasEscCommands) { "Debería contener comandos ESC para estilos" }
        assert(hasGsCommands) { "Debería contener comandos GS para tamaños de fuente" }

        println("✅ Estilos ESC/POS aplicados correctamente")
    }
}
