package com.gnico.majo.infrastructure.config

import com.gnico.majo.utils.Env
import org.junit.jupiter.api.Test
import kotlin.test.assertTrue

class ExternalDatabaseTest {

    @Test
    fun `should have getBoolean method in Env utility`() {
        // Test that the new getBoolean method works correctly
        val result1 = Env.getBoolean("NON_EXISTENT_VAR", true)
        assertTrue(result1) // Should return default value

        val result2 = Env.getBoolean("NON_EXISTENT_VAR", false)
        assertTrue(!result2) // Should return default value
    }
}
