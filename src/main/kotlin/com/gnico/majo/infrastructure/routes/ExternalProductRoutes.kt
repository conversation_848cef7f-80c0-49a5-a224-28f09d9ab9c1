package com.gnico.majo.infrastructure.routes

import com.gnico.majo.adapter.controller.rest.ExternalProductController
import io.ktor.server.application.*
import io.ktor.server.routing.*

fun Application.configureExternalProductRoutes(controller: ExternalProductController) {
    routing {
        route("/api/external-products") {
            get {
                controller.getAllExternalProducts(call)
            }

            post("/sync") {
                controller.syncExternalProducts(call)
            }

            get("/{id}") {
                controller.getExternalProductById(call)
            }
        }
    }
}
