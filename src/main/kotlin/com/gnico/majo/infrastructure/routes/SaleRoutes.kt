package com.gnico.majo.infrastructure.routes

import com.gnico.majo.adapter.controller.rest.SaleController
import com.gnico.majo.adapter.controller.rest.SaleCancellationController
import com.gnico.majo.adapter.controller.dto.ErrorResponse
import com.gnico.majo.adapter.controller.dto.SaleRequest
import com.gnico.majo.adapter.controller.dto.SaleCreatedResponse
import com.gnico.majo.adapter.controller.dto.SaleFilterRequest
import com.gnico.majo.adapter.controller.dto.SaleCancellationRequest
import kotlinx.serialization.Serializable
import io.ktor.http.HttpStatusCode
import io.ktor.server.application.Application
import io.ktor.server.request.receive
import io.ktor.server.response.respond
import io.ktor.server.routing.get
import io.ktor.server.routing.post
import io.ktor.server.routing.delete
import io.ktor.server.routing.route
import io.ktor.server.routing.routing

fun Application.configureSaleRoutes(
    saleController: SaleController,
    saleCancellationController: SaleCancellationController
) {
    routing {
        route("/api/sales") {

            post {
                try {
                    println("🔍 POST /api/sales - Recibiendo petición")
                    val request = call.receive<SaleRequest>()
                    println("🔍 Request recibido: vendedor=${request.vendedor}, items=${request.items.size}")
                    val saleId = saleController.createSale(request)
                    println("✅ Venta creada con ID: ${saleId.value}")
                    call.respond(HttpStatusCode.Created, SaleCreatedResponse(saleId.value))
                } catch (e: IllegalArgumentException) {
                    println("❌ Error de argumento: ${e.message}")
                    e.printStackTrace()
                    call.respond(HttpStatusCode.BadRequest, ErrorResponse(e.message ?: "Invalid request"))
                } catch (e: Exception) {
                    println("❌ Error interno: ${e.message}")
                    e.printStackTrace()
                    call.respond(HttpStatusCode.InternalServerError, ErrorResponse("Internal server error"))
                }
            }

            get("/scale/{codigo}") {
                try {
                    val codigo = call.parameters["codigo"]
                    if (codigo.isNullOrBlank()) {
                        call.respond(HttpStatusCode.BadRequest, ErrorResponse("Código es requerido"))
                        return@get
                    }

                    val saleDetails = saleController.getSaleDetails(codigo)
                    if (saleDetails != null) {
                        call.respond(HttpStatusCode.OK, saleDetails)
                    } else {
                        call.respond(HttpStatusCode.NotFound, ErrorResponse("Venta no encontrada"))
                    }
                } catch (e: IllegalArgumentException) {
                    call.respond(HttpStatusCode.BadRequest, ErrorResponse(e.message ?: "Invalid request"))
                } catch (e: Exception) {
                    call.respond(HttpStatusCode.InternalServerError, ErrorResponse("Internal server error"))
                }
            }

            // Nuevas rutas para consultar ventas

            // GET /api/sales/{ventaId} - Buscar venta por ID
            get("/{ventaId}") {
                try {
                    val ventaIdParam = call.parameters["ventaId"]
                    if (ventaIdParam.isNullOrBlank()) {
                        call.respond(HttpStatusCode.BadRequest, ErrorResponse("ventaId es requerido"))
                        return@get
                    }

                    val ventaId = ventaIdParam.toIntOrNull()
                    if (ventaId == null) {
                        call.respond(HttpStatusCode.BadRequest, ErrorResponse("ventaId debe ser un número válido"))
                        return@get
                    }

                    println("🔍 GET /api/sales/$ventaId")
                    val sale = saleController.getSaleById(ventaId)
                    if (sale != null) {
                        call.respond(HttpStatusCode.OK, sale)
                    } else {
                        call.respond(HttpStatusCode.NotFound, ErrorResponse("Venta no encontrada"))
                    }

                } catch (e: Exception) {
                    println("❌ Error al buscar venta: ${e.message}")
                    call.respond(HttpStatusCode.InternalServerError, ErrorResponse("Internal server error"))
                }
            }

            // GET /api/sales/numero/{numeroVenta} - Buscar venta por número
            get("/numero/{numeroVenta}") {
                try {
                    val numeroVenta = call.parameters["numeroVenta"]
                    if (numeroVenta.isNullOrBlank()) {
                        call.respond(HttpStatusCode.BadRequest, ErrorResponse("numeroVenta es requerido"))
                        return@get
                    }

                    println("🔍 GET /api/sales/numero/$numeroVenta")
                    val sale = saleController.getSaleByNumero(numeroVenta)
                    if (sale != null) {
                        call.respond(HttpStatusCode.OK, sale)
                    } else {
                        call.respond(HttpStatusCode.NotFound, ErrorResponse("Venta no encontrada"))
                    }

                } catch (e: Exception) {
                    println("❌ Error al buscar venta: ${e.message}")
                    call.respond(HttpStatusCode.InternalServerError, ErrorResponse("Internal server error"))
                }
            }

            // GET /api/sales - Buscar ventas con filtros y paginación
            get {
                try {
                    println("🔍 GET /api/sales con filtros")

                    // Extraer parámetros de query
                    val fechaDesde = call.request.queryParameters["fechaDesde"]
                    val fechaHasta = call.request.queryParameters["fechaHasta"]
                    val usuarios = call.request.queryParameters.getAll("usuarios") ?: emptyList()
                    val comprobanteEmitido = call.request.queryParameters["comprobanteEmitido"]?.toBooleanStrictOrNull()
                    val mediosPago = call.request.queryParameters.getAll("mediosPago") ?: emptyList()
                    val incluirCanceladas = call.request.queryParameters["incluirCanceladas"]?.toBooleanStrictOrNull() ?: false
                    val page = call.request.queryParameters["page"]?.toIntOrNull() ?: 1
                    val size = call.request.queryParameters["size"]?.toIntOrNull() ?: 20

                    // Validar parámetros
                    if (page < 1) {
                        call.respond(HttpStatusCode.BadRequest, ErrorResponse("page debe ser mayor o igual a 1"))
                        return@get
                    }
                    if (size < 1 || size > 100) {
                        call.respond(HttpStatusCode.BadRequest, ErrorResponse("size debe estar entre 1 y 100"))
                        return@get
                    }

                    // Crear request de filtrado
                    val filterRequest = SaleFilterRequest(
                        fechaDesde = fechaDesde,
                        fechaHasta = fechaHasta,
                        usuarios = if (usuarios.isNotEmpty()) usuarios else null,
                        comprobanteEmitido = comprobanteEmitido,
                        mediosPago = if (mediosPago.isNotEmpty()) mediosPago else null,
                        incluirCanceladas = incluirCanceladas,
                        page = page,
                        size = size
                    )

                    println("🔍 Filtros: fechaDesde=$fechaDesde, fechaHasta=$fechaHasta, usuarios=$usuarios, comprobanteEmitido=$comprobanteEmitido, mediosPago=$mediosPago, incluirCanceladas=$incluirCanceladas, page=$page, size=$size")

                    val result = saleController.getSalesWithFilters(filterRequest)
                    println("✅ Encontradas ${result.totalElements} ventas (página ${result.page}/${result.totalPages})")
                    call.respond(HttpStatusCode.OK, result)

                } catch (e: IllegalArgumentException) {
                    println("❌ Error de argumento: ${e.message}")
                    call.respond(HttpStatusCode.BadRequest, ErrorResponse(e.message ?: "Invalid request"))
                } catch (e: Exception) {
                    println("❌ Error al buscar ventas: ${e.message}")
                    e.printStackTrace()
                    call.respond(HttpStatusCode.InternalServerError, ErrorResponse("Internal server error"))
                }
            }

            // POST /api/sales/{id}/cancel - Cancelar una venta
            post("/{id}/cancel") {
                try {
                    val ventaId = call.parameters["id"]?.toIntOrNull()
                    if (ventaId == null) {
                        call.respond(HttpStatusCode.BadRequest, ErrorResponse("ID de venta inválido"))
                        return@post
                    }

                    println("🔍 POST /api/sales/$ventaId/cancel - Cancelando venta")
                    val request = call.receive<SaleCancellationRequest>()
                    println("🔍 Request: usuario=${request.usuarioCancelacion}, motivo=${request.motivo}, online=${request.generarNotaCreditoOnline}")

                    val result = saleCancellationController.cancelSale(ventaId, request)

                    if (result.success) {
                        println("✅ Venta $ventaId cancelada exitosamente")
                        call.respond(HttpStatusCode.OK, result)
                    } else {
                        println("❌ Error al cancelar venta $ventaId: ${result.error}")
                        call.respond(HttpStatusCode.BadRequest, result)
                    }

                } catch (e: IllegalArgumentException) {
                    println("❌ Error de argumento en cancelación: ${e.message}")
                    call.respond(HttpStatusCode.BadRequest, ErrorResponse(e.message ?: "Invalid request"))
                } catch (e: Exception) {
                    println("❌ Error interno en cancelación: ${e.message}")
                    e.printStackTrace()
                    call.respond(HttpStatusCode.InternalServerError, ErrorResponse("Internal server error"))
                }
            }

            // GET /api/sales/cancelled - Obtener historial de ventas canceladas
            get("/cancelled") {
                try {
                    val limit = call.request.queryParameters["limit"]?.toIntOrNull() ?: 100
                    println("🔍 GET /api/sales/cancelled - limit=$limit")

                    val result = saleCancellationController.getCancelledSalesHistory(limit)
                    println("✅ Encontradas ${result.totalCancelaciones} ventas canceladas")
                    call.respond(HttpStatusCode.OK, result)

                } catch (e: Exception) {
                    println("❌ Error al obtener ventas canceladas: ${e.message}")
                    e.printStackTrace()
                    call.respond(HttpStatusCode.InternalServerError, ErrorResponse("Internal server error"))
                }
            }

            // GET /api/sales/cancellation-stats - Obtener estadísticas de cancelaciones
            get("/cancellation-stats") {
                try {
                    println("🔍 GET /api/sales/cancellation-stats")

                    val result = saleCancellationController.getCancellationStats()
                    println("✅ Estadísticas: total=${result.totalCancelaciones}, con NC=${result.cancelacionesConNotaCredito}")
                    call.respond(HttpStatusCode.OK, result)

                } catch (e: Exception) {
                    println("❌ Error al obtener estadísticas de cancelaciones: ${e.message}")
                    e.printStackTrace()
                    call.respond(HttpStatusCode.InternalServerError, ErrorResponse("Internal server error"))
                }
            }

            // GET /api/sales/pending-nota-credito - Obtener ventas canceladas pendientes de nota de crédito
            get("/pending-nota-credito") {
                try {
                    val limit = call.request.queryParameters["limit"]?.toIntOrNull() ?: 100
                    println("🔍 GET /api/sales/pending-nota-credito - limit=$limit")

                    val result = saleCancellationController.getSalesPendingNotaCredito(limit)
                    println("✅ Encontradas ${result.totalCancelaciones} ventas pendientes de nota de crédito")
                    call.respond(HttpStatusCode.OK, result)

                } catch (e: Exception) {
                    println("❌ Error al obtener ventas pendientes de nota de crédito: ${e.message}")
                    e.printStackTrace()
                    call.respond(HttpStatusCode.InternalServerError, ErrorResponse("Internal server error"))
                }
            }
        }
    }
}
