package com.gnico.majo.adapter.controller.dto

import kotlinx.serialization.Serializable

@Serializable
data class SaleRequest(
    val vendedor: String, // username del usuario
    val medioPago: String = "EFECTIVO",
    val porcentajeDescuento: Double? = null, // Porcentaje de descuento (0-100)
    val codigoTicketBalanza: String? = null,
    val idTicketBalanza: String? = null,
    val total: Double,
    val items: List<SaleItemRequest>,
    val imprimirTicket: Boolean = false,
    val facturaOnline: Boolean = false,
    val facturaOffline: Boolean = false
)

@Serializable
data class SaleItemRequest(
    val productoNombre: String,
    val cantidad: Double,
    val precioUnitario: Double,
    val subtotal: Double,
    val unidadMedidaId: Int,
    val tipoIvaId: Int = 5 // Tipo IVA por defecto (21%)
)

@Serializable
data class SaleCreatedResponse(val saleId: Int)

// Nuevos DTOs para consulta de ventas

@Serializable
data class SaleResponse(
    val id: Int,
    val numeroVenta: String,
    val fechaVenta: String,
    val usuarioUsername: String,
    val usuarioNombre: String,
    val montoTotal: String,
    val medioPago: String,
    val porcentajeDescuento: Double?,
    val comprobanteEmitido: Boolean,
    val codigoTicketBalanza: String?,
    val idTicketBalanza: String?,
    val cancelada: Boolean,
    val fechaCancelacion: String?,
    val usuarioCancelacion: String?,
    val motivoCancelacion: String?,
    val notaCreditoGenerada: Boolean,
    val items: List<SaleItemResponse>
)

@Serializable
data class SaleItemResponse(
    val productoNombre: String,
    val cantidad: Double,
    val precioUnitario: String,
    val subtotal: String,
    val tipoIvaId: Int
)

// DTOs para filtering y paginación

@Serializable
data class SaleFilterRequest(
    val fechaDesde: String? = null,  // Formato: yyyy-MM-dd HH:mm:ss
    val fechaHasta: String? = null,  // Formato: yyyy-MM-dd HH:mm:ss
    val usuarios: List<String>? = null,  // Lista de usernames
    val comprobanteEmitido: Boolean? = null,  // true/false/null (todos)
    val mediosPago: List<String>? = null,  // Lista de medios de pago
    val incluirCanceladas: Boolean = false,  // Por defecto excluir ventas canceladas
    val page: Int = 1,  // Página (empezando en 1)
    val size: Int = 20  // Tamaño de página (máximo 100)
)

@Serializable
data class SalePageResponse(
    val content: List<SaleResponse>,
    val page: Int,
    val size: Int,
    val totalElements: Long,
    val totalPages: Int,
    val hasNext: Boolean,
    val hasPrevious: Boolean
)

@Serializable
data class ErrorResponse(val message: String)

@Serializable
data class SaleDetailResponse(
    val invoiceId: String,
    val documentNo: String,
    val datePrinted: String?,
    val totalLines: Double,
    val grandTotal: Double,
    val createdBy: String,
    val lines: List<SaleLineDetailResponse>
)

@Serializable
data class SaleLineDetailResponse(
    val line: Int,
    val productId: Int,
    val productName: String,
    val qtyInvoiced: Double,
    val priceActual: Double,
    val lineNetAmt: Double,
    val uomId: Int
)
