package com.gnico.majo.adapter.controller.rest

import com.gnico.majo.adapter.controller.dto.SaleDetailResponse
import com.gnico.majo.adapter.controller.dto.SaleLineDetailResponse
import com.gnico.majo.adapter.controller.dto.SaleRequest
import com.gnico.majo.adapter.controller.dto.SaleResponse
import com.gnico.majo.adapter.controller.dto.SaleItemResponse
import com.gnico.majo.adapter.controller.dto.SaleFilterRequest
import com.gnico.majo.adapter.controller.dto.SalePageResponse
import com.gnico.majo.application.domain.model.ExternalSaleDetail
import com.gnico.majo.application.domain.model.Id
import com.gnico.majo.application.domain.model.Sale
import com.gnico.majo.application.port.`in`.SaleService
import java.time.format.DateTimeFormatter
import java.text.DecimalFormat
import kotlinx.serialization.Serializable

class SaleController(
    private val saleService: SaleService
) {
    private val dateFormatter = DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm")
    private val decimalFormatter = DecimalFormat("#,##0.00")
    suspend fun createSale(request: SaleRequest): Id {
        return saleService.createSale(
            vendedor = request.vendedor,
            itemsRequest = request.items,
            medioPago = request.medioPago,
            porcentajeDescuento = request.porcentajeDescuento?.let { java.math.BigDecimal.valueOf(it) },
            codigoTicketBalanza = request.codigoTicketBalanza,
            idTicketBalanza = request.idTicketBalanza,
            imprimirTicket = request.imprimirTicket,
            facturaOnline = request.facturaOnline,
            facturaOffline = request.facturaOffline
        )
    }

    suspend fun getSaleDetails(codigo: String): SaleDetailResponse? {
        val externalSaleDetail = saleService.getExternalSaleDetails(codigo)
        return externalSaleDetail?.let { mapToSaleDetailResponse(it) }
    }

    private fun mapToSaleDetailResponse(externalSale: ExternalSaleDetail): SaleDetailResponse {
        return SaleDetailResponse(
            invoiceId = externalSale.invoiceId,
            documentNo = externalSale.documentNo,
            datePrinted = externalSale.datePrinted?.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME),
            totalLines = externalSale.totalLines.toDouble(),
            grandTotal = externalSale.grandTotal.toDouble(),
            createdBy = externalSale.createdBy,
            lines = externalSale.lines.map { line ->
                SaleLineDetailResponse(
                    line = line.line,
                    productId = line.productId,
                    productName = line.productName,
                    qtyInvoiced = line.qtyInvoiced.toDouble(),
                    priceActual = line.priceActual.toDouble(),
                    lineNetAmt = line.lineNetAmt.toDouble(),
                    uomId = line.uomId
                )
            }
        )
    }

    // Nuevos métodos para consultar ventas internas

    /**
     * Busca una venta por su ID
     */
    fun getSaleById(ventaId: Int): SaleResponse? {
        val sale = saleService.findSaleById(Id(ventaId))
        return sale?.let { mapToSaleResponse(it) }
    }

    /**
     * Busca una venta por su número
     */
    fun getSaleByNumero(numeroVenta: String): SaleResponse? {
        val sale = saleService.findSaleByNumero(numeroVenta)
        return sale?.let { mapToSaleResponse(it) }
    }

    /**
     * Busca ventas por rango de fechas
     */
    fun getSalesByDateRange(fechaDesde: String, fechaHasta: String): List<SaleResponse> {
        val sales = saleService.findSalesByDateRange(fechaDesde, fechaHasta)
        return sales.map { mapToSaleResponse(it) }
    }

    /**
     * Busca ventas por usuario
     */
    fun getSalesByUsuario(username: String): List<SaleResponse> {
        val sales = saleService.findSalesByUsuario(username)
        return sales.map { mapToSaleResponse(it) }
    }

    /**
     * Busca ventas con filtros y paginación
     */
    fun getSalesWithFilters(filterRequest: SaleFilterRequest): SalePageResponse {
        return saleService.findSalesWithFilters(filterRequest)
    }

    /**
     * Mapea una venta del dominio a DTO de respuesta
     */
    private fun mapToSaleResponse(sale: Sale): SaleResponse {
        return SaleResponse(
            id = sale.id?.value ?: throw IllegalStateException("Venta sin ID"),
            numeroVenta = sale.numeroVenta,
            fechaVenta = sale.fechaVenta.format(dateFormatter),
            usuarioUsername = sale.usuario.username,
            usuarioNombre = sale.usuario.nombreDisplay,
            montoTotal = decimalFormatter.format(sale.montoTotal),
            medioPago = sale.medioPago,
            porcentajeDescuento = sale.porcentajeDescuento?.toDouble(),
            comprobanteEmitido = sale.comprobanteEmitido,
            codigoTicketBalanza = sale.codigoTicketBalanza,
            idTicketBalanza = sale.idTicketBalanza,
            cancelada = sale.cancelada,
            fechaCancelacion = sale.fechaCancelacion?.format(dateFormatter),
            usuarioCancelacion = sale.usuarioCancelacion,
            motivoCancelacion = sale.motivoCancelacion,
            notaCreditoGenerada = sale.notaCreditoGenerada,
            items = sale.items.map { item ->
                SaleItemResponse(
                    productoNombre = item.productoNombre,
                    cantidad = item.cantidad.toDouble(),
                    precioUnitario = decimalFormatter.format(item.precioUnitario),
                    subtotal = decimalFormatter.format(item.subtotal),
                    tipoIvaId = item.tipoIva.id
                )
            }
        )
    }
}