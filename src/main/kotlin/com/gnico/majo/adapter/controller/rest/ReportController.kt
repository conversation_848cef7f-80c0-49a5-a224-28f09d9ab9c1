package com.gnico.majo.adapter.controller.rest

import com.gnico.majo.application.port.`in`.ReportService
import com.gnico.majo.application.domain.model.*
import com.gnico.majo.adapter.controller.rest.dto.ErrorResponse
import io.ktor.http.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import kotlinx.serialization.Serializable
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.time.format.DateTimeParseException

/**
 * Controlador REST para reportes y estadísticas
 */
class ReportController(
    private val reportService: ReportService
) {

    /**
     * Obtiene un reporte completo de ventas
     */
    suspend fun getSalesReport(
        periodo: String?,
        fechaDesde: String?,
        fechaHasta: String?,
        usuarios: List<String>?,
        mediosPago: List<String>?,
        incluirCanceladas: Boolean,
        soloConComprobante: Boolean?,
        soloMediosPagoElectronicos: Boolean
    ): SalesReportResponse {
        try {
            val filters = createReportFilters(
                periodo = periodo,
                fechaDesde = fechaDesde,
                fechaHasta = fechaHasta,
                usuarios = usuarios,
                mediosPago = mediosPago,
                incluirCanceladas = incluirCanceladas,
                soloConComprobante = soloConComprobante,
                soloMediosPagoElectronicos = soloMediosPagoElectronicos
            )

            val report = reportService.getSalesReport(filters)
            return SalesReportResponse.fromDomain(report)

        } catch (e: IllegalArgumentException) {
            throw IllegalArgumentException("Error en parámetros: ${e.message}")
        } catch (e: Exception) {
            throw RuntimeException("Error interno al generar reporte de ventas", e)
        }
    }



    /**
     * Crea filtros de reporte desde parámetros HTTP
     */
    private fun createReportFilters(
        periodo: String?,
        fechaDesde: String?,
        fechaHasta: String?,
        usuarios: List<String>?,
        mediosPago: List<String>?,
        incluirCanceladas: Boolean,
        soloConComprobante: Boolean?,
        soloMediosPagoElectronicos: Boolean
    ): ReportFilters {
        val reportPeriod = when {
            periodo != null -> ReportPeriod.fromString(periodo)
            fechaDesde != null && fechaHasta != null -> {
                try {
                    val formatter = DateTimeFormatter.ISO_LOCAL_DATE_TIME
                    val desde = LocalDateTime.parse(fechaDesde, formatter)
                    val hasta = LocalDateTime.parse(fechaHasta, formatter)
                    ReportPeriod.custom(desde, hasta)
                } catch (e: DateTimeParseException) {
                    throw IllegalArgumentException("Formato de fecha inválido. Use ISO format: yyyy-MM-ddTHH:mm:ss")
                }
            }
            else -> ReportPeriod.today()
        }

        return ReportFilters(
            periodo = reportPeriod,
            usuarios = usuarios?.filter { it.isNotBlank() },
            mediosPago = mediosPago?.filter { it.isNotBlank() },
            incluirCanceladas = incluirCanceladas,
            soloConComprobante = soloConComprobante,
            soloMediosPagoElectronicos = soloMediosPagoElectronicos
        )
    }
}

// ========== DTOs DE RESPUESTA ==========

@Serializable
data class SalesReportResponse(
    val periodo: ReportPeriodResponse,
    val resumenGeneral: SalesGeneralSummaryResponse,
    val desglosePorMedioPago: List<PaymentMethodBreakdownResponse>,
    val desglosePorVendedor: List<SellerBreakdownResponse>,
    val productosTopVentas: List<ProductBreakdownResponse>,
    val estadisticasComprobantes: ComprobanteStatisticsResponse,
    val alertas: ReportAlertsResponse,
    val histograma: SalesHistogramResponse
) {
    companion object {
        fun fromDomain(domain: SalesReport): SalesReportResponse {
            return SalesReportResponse(
                periodo = ReportPeriodResponse.fromDomain(domain.periodo),
                resumenGeneral = SalesGeneralSummaryResponse.fromDomain(domain.resumenGeneral),
                desglosePorMedioPago = domain.desglosePorMedioPago.map { PaymentMethodBreakdownResponse.fromDomain(it) },
                desglosePorVendedor = domain.desglosePorVendedor.map { SellerBreakdownResponse.fromDomain(it) },
                productosTopVentas = domain.productosTopVentas.map { ProductBreakdownResponse.fromDomain(it) },
                estadisticasComprobantes = ComprobanteStatisticsResponse.fromDomain(domain.estadisticasComprobantes),
                alertas = ReportAlertsResponse.fromDomain(domain.alertas),
                histograma = SalesHistogramResponse.fromDomain(domain.histograma)
            )
        }
    }
}

@Serializable
data class ComprobanteReportResponse(
    val periodo: ReportPeriodResponse,
    val totalFacturado: Double,
    val totalIva: Double,
    val balanceNeto: Double,
    val desglosePorTipo: List<ComprobanteTypeBreakdownResponse>,
    val notasCredito: CreditNotesSummaryResponse
) {
    companion object {
        fun fromDomain(domain: ComprobanteReport): ComprobanteReportResponse {
            return ComprobanteReportResponse(
                periodo = ReportPeriodResponse.fromDomain(domain.periodo),
                totalFacturado = domain.totalFacturado.toDouble(),
                totalIva = domain.totalIva.toDouble(),
                balanceNeto = domain.balanceNeto.toDouble(),
                desglosePorTipo = domain.desglosePorTipo.map { ComprobanteTypeBreakdownResponse.fromDomain(it) },
                notasCredito = CreditNotesSummaryResponse.fromDomain(domain.notasCredito)
            )
        }
    }
}



@Serializable
data class ReportPeriodResponse(
    val desde: String,
    val hasta: String,
    val descripcion: String
) {
    companion object {
        fun fromDomain(domain: ReportPeriod): ReportPeriodResponse {
            val formatter = DateTimeFormatter.ISO_LOCAL_DATE_TIME
            return ReportPeriodResponse(
                desde = domain.desde.format(formatter),
                hasta = domain.hasta.format(formatter),
                descripcion = domain.descripcion
            )
        }
    }
}

@Serializable
data class SalesGeneralSummaryResponse(
    val totalVentas: Int,
    val montoTotal: Double,
    val ticketPromedio: Double,
    val ventasConComprobante: Int,
    val ventasSinComprobante: Int,
    val montoConComprobante: Double,
    val montoSinComprobante: Double,
    val ventasCanceladas: Int,
    val montoCancelado: Double,
    val ventasElectronicasPendientes: Int,
    val montoElectronicosPendientes: Double
) {
    companion object {
        fun fromDomain(domain: SalesGeneralSummary): SalesGeneralSummaryResponse {
            return SalesGeneralSummaryResponse(
                totalVentas = domain.totalVentas,
                montoTotal = domain.montoTotal.toDouble(),
                ticketPromedio = domain.ticketPromedio.toDouble(),
                ventasConComprobante = domain.ventasConComprobante,
                ventasSinComprobante = domain.ventasSinComprobante,
                montoConComprobante = domain.montoConComprobante.toDouble(),
                montoSinComprobante = domain.montoSinComprobante.toDouble(),
                ventasCanceladas = domain.ventasCanceladas,
                montoCancelado = domain.montoCancelado.toDouble(),
                ventasElectronicasPendientes = domain.ventasElectronicasPendientes,
                montoElectronicosPendientes = domain.montoElectronicosPendientes.toDouble()
            )
        }
    }
}

@Serializable
data class PaymentMethodBreakdownResponse(
    val medioPago: String,
    val descripcion: String,
    val cantidadVentas: Int,
    val montoTotal: Double,
    val porcentajeDelTotal: Double,
    val ticketPromedio: Double,
    val esElectronico: Boolean
) {
    companion object {
        fun fromDomain(domain: PaymentMethodBreakdown): PaymentMethodBreakdownResponse {
            return PaymentMethodBreakdownResponse(
                medioPago = domain.medioPago,
                descripcion = domain.descripcion,
                cantidadVentas = domain.cantidadVentas,
                montoTotal = domain.montoTotal.toDouble(),
                porcentajeDelTotal = domain.porcentajeDelTotal.toDouble(),
                ticketPromedio = domain.ticketPromedio.toDouble(),
                esElectronico = domain.esElectronico
            )
        }
    }
}

@Serializable
data class SellerBreakdownResponse(
    val username: String,
    val nombreDisplay: String,
    val cantidadVentas: Int,
    val montoTotal: Double,
    val ticketPromedio: Double,
    val porcentajeDelTotal: Double,
    val ventasConComprobante: Int,
    val ventasSinComprobante: Int
) {
    companion object {
        fun fromDomain(domain: SellerBreakdown): SellerBreakdownResponse {
            return SellerBreakdownResponse(
                username = domain.username,
                nombreDisplay = domain.nombreDisplay,
                cantidadVentas = domain.cantidadVentas,
                montoTotal = domain.montoTotal.toDouble(),
                ticketPromedio = domain.ticketPromedio.toDouble(),
                porcentajeDelTotal = domain.porcentajeDelTotal.toDouble(),
                ventasConComprobante = domain.ventasConComprobante,
                ventasSinComprobante = domain.ventasSinComprobante
            )
        }
    }
}

@Serializable
data class ProductBreakdownResponse(
    val productoNombre: String,
    val cantidadVendida: Double,
    val unidadMedida: String,
    val montoTotal: Double,
    val cantidadTransacciones: Int
) {
    companion object {
        fun fromDomain(domain: ProductBreakdown): ProductBreakdownResponse {
            return ProductBreakdownResponse(
                productoNombre = domain.productoNombre,
                cantidadVendida = domain.cantidadVendida.toDouble(),
                unidadMedida = domain.unidadMedida,
                montoTotal = domain.montoTotal.toDouble(),
                cantidadTransacciones = domain.cantidadTransacciones
            )
        }
    }
}

@Serializable
data class ComprobanteStatisticsResponse(
    val totalComprobantes: Int,
    val montoTotalFacturado: Double,
    val montoTotalIva: Double,
    val desglosePorTipo: List<ComprobanteTypeBreakdownResponse>,
    val notasCredito: CreditNotesSummaryResponse
) {
    companion object {
        fun fromDomain(domain: ComprobanteStatistics): ComprobanteStatisticsResponse {
            return ComprobanteStatisticsResponse(
                totalComprobantes = domain.totalComprobantes,
                montoTotalFacturado = domain.montoTotalFacturado.toDouble(),
                montoTotalIva = domain.montoTotalIva.toDouble(),
                desglosePorTipo = domain.desglosePorTipo.map { ComprobanteTypeBreakdownResponse.fromDomain(it) },
                notasCredito = CreditNotesSummaryResponse.fromDomain(domain.notasCredito)
            )
        }
    }
}

@Serializable
data class ComprobanteTypeBreakdownResponse(
    val tipoComprobante: String,
    val cantidad: Int,
    val montoTotal: Double,
    val montoIva: Double
) {
    companion object {
        fun fromDomain(domain: ComprobanteTypeBreakdown): ComprobanteTypeBreakdownResponse {
            return ComprobanteTypeBreakdownResponse(
                tipoComprobante = domain.tipoComprobante,
                cantidad = domain.cantidad,
                montoTotal = domain.montoTotal.toDouble(),
                montoIva = domain.montoIva.toDouble()
            )
        }
    }
}

@Serializable
data class CreditNotesSummaryResponse(
    val cantidadNotas: Int,
    val montoTotal: Double,
    val montoIva: Double,
    val ventasPendientesNotaCredito: Int
) {
    companion object {
        fun fromDomain(domain: CreditNotesSummary): CreditNotesSummaryResponse {
            return CreditNotesSummaryResponse(
                cantidadNotas = domain.cantidadNotas,
                montoTotal = domain.montoTotal.toDouble(),
                montoIva = domain.montoIva.toDouble(),
                ventasPendientesNotaCredito = domain.ventasPendientesNotaCredito
            )
        }
    }
}

@Serializable
data class ReportAlertsResponse(
    val ventasSinComprobante: Int,
    val montoSinComprobante: Double,
    val ventasElectronicasSinComprobante: Int,
    val montoElectronicosSinComprobante: Double,
    val ventasCanceladasPendientesNC: Int
) {
    companion object {
        fun fromDomain(domain: ReportAlerts): ReportAlertsResponse {
            return ReportAlertsResponse(
                ventasSinComprobante = domain.ventasSinComprobante,
                montoSinComprobante = domain.montoSinComprobante.toDouble(),
                ventasElectronicasSinComprobante = domain.ventasElectronicasSinComprobante,
                montoElectronicosSinComprobante = domain.montoElectronicosSinComprobante.toDouble(),
                ventasCanceladasPendientesNC = domain.ventasCanceladasPendientesNC
            )
        }
    }
}

@Serializable
data class SalesHistogramResponse(
    val periodo: ReportPeriodResponse,
    val bucketType: String,
    val buckets: List<SalesHistogramBucketResponse>,
    val resumen: SalesHistogramSummaryResponse
) {
    companion object {
        fun fromDomain(domain: SalesHistogram): SalesHistogramResponse {
            return SalesHistogramResponse(
                periodo = ReportPeriodResponse.fromDomain(domain.periodo),
                bucketType = domain.bucketType.name,
                buckets = domain.buckets.map { SalesHistogramBucketResponse.fromDomain(it) },
                resumen = SalesHistogramSummaryResponse.fromDomain(domain.resumen)
            )
        }
    }
}

@Serializable
data class SalesHistogramBucketResponse(
    val periodo: String,
    val fechaInicio: String,
    val fechaFin: String,
    val cantidadVentas: Int,
    val montoTotal: Double
) {
    companion object {
        fun fromDomain(domain: SalesHistogramBucket): SalesHistogramBucketResponse {
            return SalesHistogramBucketResponse(
                periodo = domain.periodo,
                fechaInicio = domain.fechaInicio.toString(),
                fechaFin = domain.fechaFin.toString(),
                cantidadVentas = domain.cantidadVentas,
                montoTotal = domain.montoTotal.toDouble()
            )
        }
    }
}

@Serializable
data class SalesHistogramSummaryResponse(
    val totalVentas: Int,
    val montoTotal: Double,
    val promedioVentasPorBucket: Double,
    val promedioMontoPorBucket: Double,
    val bucketConMasVentas: SalesHistogramBucketResponse?,
    val bucketConMayorMonto: SalesHistogramBucketResponse?
) {
    companion object {
        fun fromDomain(domain: SalesHistogramSummary): SalesHistogramSummaryResponse {
            return SalesHistogramSummaryResponse(
                totalVentas = domain.totalVentas,
                montoTotal = domain.montoTotal.toDouble(),
                promedioVentasPorBucket = domain.promedioVentasPorBucket,
                promedioMontoPorBucket = domain.promedioMontoPorBucket.toDouble(),
                bucketConMasVentas = domain.bucketConMasVentas?.let { SalesHistogramBucketResponse.fromDomain(it) },
                bucketConMayorMonto = domain.bucketConMayorMonto?.let { SalesHistogramBucketResponse.fromDomain(it) }
            )
        }
    }
}

