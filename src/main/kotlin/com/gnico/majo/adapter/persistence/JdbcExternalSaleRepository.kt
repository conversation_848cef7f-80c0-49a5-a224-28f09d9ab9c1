package com.gnico.majo.adapter.persistence

import com.gnico.majo.application.domain.model.ExternalSaleDetail
import com.gnico.majo.application.domain.model.ExternalSaleLineDetail
import com.gnico.majo.application.port.out.ExternalSaleRepositoryPort
import com.gnico.majo.infrastructure.config.ExternalDatabase
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.math.BigDecimal
import java.sql.SQLException
import java.sql.Timestamp
import java.time.LocalDateTime
import kotlin.io.use

class JdbcExternalSaleRepository : ExternalSaleRepositoryPort {

    override suspend fun findSaleDetailsByDocumentNo(documentNo: String): ExternalSaleDetail? = withContext(Dispatchers.IO) {
        try {
            val connection = ExternalDatabase.getConnection()
            if (connection == null) {
                println("⚠ Base de datos externa no disponible - no se puede buscar venta $documentNo")
                return@withContext null
            }

            connection.use { conn ->
                val sql = """
                    select 
                    i.invoice_id,
                    i.documentno,
                    i.dateprinted,
                    i.totallines,
                    i.grandtotal,
                    i.createdby,
                    l.line,
                    l.product_id, 
                    p.name,
                    l.qtyinvoiced,
                    l.priceactual,
                    l.linenetamt,
                    l.uom_id
                    from invoice i 
                    join invoiceline l
                    on i.invoice_id = l.invoice_id 
                    join product p 
                    on l.product_id = p.product_id
                    where i.documentno = ?
                    order by l.line
                """.trimIndent()

                conn.prepareStatement(sql).use { statement ->
                    statement.setString(1, documentNo)
                    statement.executeQuery().use { resultSet ->

                        var saleHeader: ExternalSaleDetail? = null
                        val lines = mutableListOf<ExternalSaleLineDetail>()

                        while (resultSet.next()) {
                            // Si es la primera fila, crear el header de la venta
                            if (saleHeader == null) {
                                val datePrintedTimestamp = resultSet.getTimestamp("dateprinted")
                                val datePrinted = datePrintedTimestamp?.toLocalDateTime()

                                // Obtener invoice_id y createdby como strings
                                val invoiceId = resultSet.getString("invoice_id") ?: ""
                                val createdBy = resultSet.getString("createdby") ?: ""

                                saleHeader = ExternalSaleDetail(
                                    invoiceId = invoiceId,
                                    documentNo = resultSet.getString("documentno"),
                                    datePrinted = datePrinted,
                                    totalLines = resultSet.getBigDecimal("totallines") ?: BigDecimal.ZERO,
                                    grandTotal = resultSet.getBigDecimal("grandtotal") ?: BigDecimal.ZERO,
                                    createdBy = createdBy,
                                    lines = emptyList() // Se asignará después
                                )
                            }

                            // Agregar línea de detalle
                            val lineStr = resultSet.getString("line")
                            val line = lineStr?.toIntOrNull() ?: 0

                            val productIdStr = resultSet.getString("product_id")
                            val productId = productIdStr?.toIntOrNull() ?: 0

                            val uomIdStr = resultSet.getString("uom_id")
                            val uomId = uomIdStr?.toIntOrNull() ?: 0

                            val lineDetail = ExternalSaleLineDetail(
                                line = line,
                                productId = productId,
                                productName = resultSet.getString("name") ?: "",
                                qtyInvoiced = resultSet.getBigDecimal("qtyinvoiced") ?: BigDecimal.ZERO,
                                priceActual = resultSet.getBigDecimal("priceactual") ?: BigDecimal.ZERO,
                                lineNetAmt = resultSet.getBigDecimal("linenetamt") ?: BigDecimal.ZERO,
                                uomId = uomId
                            )
                            lines.add(lineDetail)
                        }

                        // Retornar el header con las líneas asignadas
                        return@withContext saleHeader?.copy(lines = lines)
                    }
                }
            }
        } catch (e: SQLException) {
            println("⚠ Error al obtener detalles de venta $documentNo de la base de datos externa: ${e.message}")
            return@withContext null
        }
    }
}
