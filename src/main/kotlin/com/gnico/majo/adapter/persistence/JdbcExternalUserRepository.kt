package com.gnico.majo.adapter.persistence

import com.gnico.majo.application.port.out.ExternalUser
import com.gnico.majo.application.port.out.ExternalUserRepositoryPort
import com.gnico.majo.infrastructure.config.ExternalDatabase
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.sql.SQLException

class JdbcExternalUserRepository : ExternalUserRepositoryPort {

    override suspend fun findAllUsers(): List<ExternalUser> = withContext(Dispatchers.IO) {
        val users = mutableListOf<ExternalUser>()

        if (!ExternalDatabase.isAvailable()) {
            println("⚠ Base de datos externa no disponible - retornando lista vacía de usuarios")
            return@withContext emptyList()
        }

        try {
            ExternalDatabase.getConnection()?.use { connection ->
                val sql = """
                    SELECT u.username, u.name
                    FROM tb_user u
                    WHERE default_role_id = '3'
                """.trimIndent()

                connection.prepareStatement(sql).use { statement ->
                    statement.executeQuery().use { resultSet ->
                        while (resultSet.next()) {
                            val username = resultSet.getString("username")
                            val name = resultSet.getString("name")

                            if (!resultSet.wasNull() && username != null && name != null) {
                                users.add(ExternalUser(username, name))
                            }
                        }
                    }
                }
            } ?: run {
                println("⚠ No se pudo obtener conexión a la base de datos externa")
                return@withContext emptyList()
            }
        } catch (e: SQLException) {
            println("⚠ Error al obtener usuarios de la base de datos externa: ${e.message}")
            return@withContext emptyList()
        }

        return@withContext users
    }
}
