package com.gnico.majo.adapter.persistence

import com.gnico.majo.application.domain.model.CaeaCode
import com.gnico.majo.application.domain.model.EstadoCaea
import com.gnico.majo.application.port.out.CaeaRepositoryPort
import com.gnico.majo.application.port.out.ComprobanteCAEAInfo
import com.gnico.majo.infrastructure.config.Database
import org.jooq.Record
import org.jooq.impl.DSL
import org.slf4j.LoggerFactory
import java.time.LocalDate
import java.time.LocalDateTime

/**
 * Implementación JOOQ del repositorio de códigos CAEA
 */
class JooqCaeaRepository : CaeaRepositoryPort {
    
    private val logger = LoggerFactory.getLogger(JooqCaeaRepository::class.java)
    
    // Nombres de tabla y columnas (hasta que se genere con JOOQ)
    private val CAEA_CODES = org.jooq.impl.DSL.table("caea_codes")
    private val ID = org.jooq.impl.DSL.field("id", Int::class.java)
    private val CAEA = org.jooq.impl.DSL.field("caea", String::class.java)
    private val PUNTO_VENTA = org.jooq.impl.DSL.field("punto_venta", Int::class.java)
    private val PERIODO = org.jooq.impl.DSL.field("periodo", String::class.java)
    private val FECHA_DESDE = org.jooq.impl.DSL.field("fecha_desde", LocalDate::class.java)
    private val FECHA_HASTA = org.jooq.impl.DSL.field("fecha_hasta", LocalDate::class.java)
    private val ORDEN = org.jooq.impl.DSL.field("orden", Int::class.java)
    private val ESTADO = org.jooq.impl.DSL.field("estado", String::class.java)
    private val ULTIMO_NUMERO_FACTURA_B = org.jooq.impl.DSL.field("ultimo_numero_factura_b", Long::class.java)
    private val ULTIMO_NUMERO_NOTA_CREDITO_B = org.jooq.impl.DSL.field("ultimo_numero_nota_credito_b", Long::class.java)
    private val ULTIMO_NUMERO_NOTA_DEBITO_B = org.jooq.impl.DSL.field("ultimo_numero_nota_debito_b", Long::class.java)
    private val CREADO_EN = org.jooq.impl.DSL.field("creado_en", java.time.LocalDateTime::class.java)
    private val ACTUALIZADO_EN = org.jooq.impl.DSL.field("actualizado_en", java.time.LocalDateTime::class.java)
    
    override fun findActiveCaea(puntoVenta: Int, fecha: LocalDate): CaeaCode? {
        logger.debug("Buscando CAEA activo para punto de venta $puntoVenta en fecha $fecha")

        return Database.dsl.transactionResult { config ->
            val dsl = DSL.using(config)
            dsl.selectFrom(CAEA_CODES)
                .where(PUNTO_VENTA.eq(puntoVenta))
                .and(ESTADO.eq("ACTIVO"))
                .and(FECHA_DESDE.le(fecha))
                .and(FECHA_HASTA.ge(fecha))
                .orderBy(ORDEN.asc()) // Preferir orden 1 sobre orden 2
                .limit(1)
                .fetchOne()
                ?.let { mapToCaeaCode(it) }
        }
    }
    
    override fun getNextComprobanteNumber(caeaId: Int, tipoComprobante: String): Long {
        val fieldName = when (tipoComprobante) {
            "FACTURA_B" -> ULTIMO_NUMERO_FACTURA_B
            "NOTA_CREDITO_B" -> ULTIMO_NUMERO_NOTA_CREDITO_B
            "NOTA_DEBITO_B" -> ULTIMO_NUMERO_NOTA_DEBITO_B
            else -> throw IllegalArgumentException("Tipo de comprobante no soportado: $tipoComprobante")
        }

        return Database.dsl.transactionResult { config ->
            val dsl = DSL.using(config)
            val currentNumber = dsl.select(fieldName)
                .from(CAEA_CODES)
                .where(ID.eq(caeaId))
                .fetchOne(fieldName) ?: 0L

            currentNumber + 1
        }
    }
    
    override fun updateLastComprobanteNumber(caeaId: Int, tipoComprobante: String, numeroComprobante: Long) {
        val fieldName = when (tipoComprobante) {
            "FACTURA_B" -> ULTIMO_NUMERO_FACTURA_B
            "NOTA_CREDITO_B" -> ULTIMO_NUMERO_NOTA_CREDITO_B
            "NOTA_DEBITO_B" -> ULTIMO_NUMERO_NOTA_DEBITO_B
            else -> throw IllegalArgumentException("Tipo de comprobante no soportado: $tipoComprobante")
        }

        Database.dsl.transactionResult { config ->
            val dsl = DSL.using(config)
            val updated = dsl.update(CAEA_CODES)
                .set(fieldName, numeroComprobante)
                .set(ACTUALIZADO_EN, java.time.LocalDateTime.now())
                .where(ID.eq(caeaId))
                .execute()

            if (updated == 0) {
                throw IllegalStateException("No se pudo actualizar el número de comprobante para CAEA $caeaId")
            }

            logger.debug("Actualizado último número $tipoComprobante a $numeroComprobante para CAEA $caeaId")
        }
    }
    
    override fun markAsExhausted(caeaId: Int) {
        Database.dsl.transactionResult { config ->
            val dsl = DSL.using(config)
            val updated = dsl.update(CAEA_CODES)
                .set(ESTADO, "AGOTADO")
                .set(ACTUALIZADO_EN, java.time.LocalDateTime.now())
                .where(ID.eq(caeaId))
                .execute()

            if (updated == 0) {
                throw IllegalStateException("No se pudo marcar como agotado el CAEA $caeaId")
            }

            logger.info("CAEA $caeaId marcado como agotado")
        }
    }

    override fun markExpiredCaeas(fecha: LocalDate): Int {
        return Database.dsl.transactionResult { config ->
            val dsl = DSL.using(config)
            val updated = dsl.update(CAEA_CODES)
                .set(ESTADO, "VENCIDO")
                .set(ACTUALIZADO_EN, java.time.LocalDateTime.now())
                .where(ESTADO.eq("ACTIVO"))
                .and(FECHA_HASTA.lt(fecha))
                .execute()

            if (updated > 0) {
                logger.info("Marcados $updated CAEAs como vencidos")
            }

            updated
        }
    }
    
    override fun findByPuntoVenta(puntoVenta: Int): List<CaeaCode> {
        return Database.dsl.transactionResult { config ->
            val dsl = DSL.using(config)
            dsl.selectFrom(CAEA_CODES)
                .where(PUNTO_VENTA.eq(puntoVenta))
                .orderBy(PERIODO.desc(), ORDEN.asc())
                .fetch()
                .map { mapToCaeaCode(it) }
        }
    }
    
    override fun save(caea: CaeaCode): CaeaCode {
        return Database.dsl.transactionResult { config ->
            val dsl = DSL.using(config)
            if (caea.id == null) {
                // Insertar nuevo
                val newId = dsl.insertInto(CAEA_CODES)
                    .set(CAEA, caea.caea)
                    .set(PUNTO_VENTA, caea.puntoVenta)
                    .set(PERIODO, caea.periodo)
                    .set(FECHA_DESDE, caea.fechaDesde)
                    .set(FECHA_HASTA, caea.fechaHasta)
                    .set(ORDEN, caea.orden)
                    .set(ESTADO, caea.estado.name)
                    .set(ULTIMO_NUMERO_FACTURA_B, caea.ultimoNumeroFacturaB)
                    .set(ULTIMO_NUMERO_NOTA_CREDITO_B, caea.ultimoNumeroNotaCreditoB)
                    .set(ULTIMO_NUMERO_NOTA_DEBITO_B, caea.ultimoNumeroNotaDebitoB)
                    .returningResult(ID)
                    .fetchOne()
                    ?.getValue(ID)
                    ?: throw IllegalStateException("No se pudo insertar el CAEA")

                caea.copy(id = newId)
            } else {
                // Actualizar existente
                val updated = dsl.update(CAEA_CODES)
                    .set(CAEA, caea.caea)
                    .set(PUNTO_VENTA, caea.puntoVenta)
                    .set(PERIODO, caea.periodo)
                    .set(FECHA_DESDE, caea.fechaDesde)
                    .set(FECHA_HASTA, caea.fechaHasta)
                    .set(ORDEN, caea.orden)
                    .set(ESTADO, caea.estado.name)
                    .set(ULTIMO_NUMERO_FACTURA_B, caea.ultimoNumeroFacturaB)
                    .set(ULTIMO_NUMERO_NOTA_CREDITO_B, caea.ultimoNumeroNotaCreditoB)
                    .set(ULTIMO_NUMERO_NOTA_DEBITO_B, caea.ultimoNumeroNotaDebitoB)
                    .set(ACTUALIZADO_EN, java.time.LocalDateTime.now())
                    .where(ID.eq(caea.id))
                    .execute()

                if (updated == 0) {
                    throw IllegalStateException("No se pudo actualizar el CAEA ${caea.id}")
                }

                caea
            }
        }
    }
    
    override fun findByCaeaCode(caeaCode: String): CaeaCode? {
        return Database.dsl.transactionResult { config ->
            val dsl = DSL.using(config)
            dsl.selectFrom(CAEA_CODES)
                .where(CAEA.eq(caeaCode))
                .fetchOne()
                ?.let { mapToCaeaCode(it) }
        }
    }
    
    override fun findComprobantesEmitidosConCAEA(caeaCode: String): List<ComprobanteCAEAInfo> {
        return Database.dsl.transactionResult { config ->
            val dsl = DSL.using(config)

            // Consultar comprobantes que fueron emitidos con este CAEA
            // Esto requiere que los comprobantes tengan referencia al CAEA utilizado
            dsl.select(
                DSL.field("numero_comprobante", Long::class.java),
                DSL.field("tipo_comprobante", String::class.java),
                DSL.field("fecha_emision", LocalDate::class.java),
                DSL.field("imp_total", java.math.BigDecimal::class.java),
                DSL.field("imp_neto", java.math.BigDecimal::class.java),
                DSL.field("imp_iva", java.math.BigDecimal::class.java),
                DSL.field("imp_trib", java.math.BigDecimal::class.java),
                DSL.field("imp_tot_conc", java.math.BigDecimal::class.java),
                DSL.field("caea_utilizado", String::class.java)
            )
            .from("comprobantes")
            .where(DSL.field("caea_utilizado").eq(caeaCode))
            .and(DSL.field("estado").eq("AUTORIZADO"))
            .orderBy(DSL.field("numero_comprobante"))
            .fetch()
            .map { record ->
                ComprobanteCAEAInfo(
                    numeroComprobante = record.getValue("numero_comprobante", Long::class.java),
                    tipoComprobante = record.getValue("tipo_comprobante", String::class.java),
                    fechaEmision = record.getValue("fecha_emision", LocalDate::class.java),
                    montoTotal = record.getValue("imp_total", java.math.BigDecimal::class.java),
                    montoNeto = record.getValue("imp_neto", java.math.BigDecimal::class.java),
                    montoIva = record.getValue("imp_iva", java.math.BigDecimal::class.java),
                    montoTributos = record.getValue("imp_trib", java.math.BigDecimal::class.java),
                    montoTotalConceptos = record.getValue("imp_tot_conc", java.math.BigDecimal::class.java),
                    montoExento = java.math.BigDecimal.ZERO, // Siempre 0 según especificación
                    caeaUtilizado = record.getValue("caea_utilizado", String::class.java)
                )
            }
        }
    }

    override fun markAsInformado(caeaCode: String, tipoInforme: String) {
        Database.dsl.transactionResult { config ->
            val dsl = DSL.using(config)

            val updated = dsl.update(CAEA_CODES)
                .set(DSL.field("informado", Boolean::class.java), true)
                .set(DSL.field("tipo_informe", String::class.java), tipoInforme)
                .set(DSL.field("fecha_informe", java.time.LocalDateTime::class.java), java.time.LocalDateTime.now())
                .set(ESTADO, EstadoCaea.INFORMADO.name) // Actualizar estado a INFORMADO
                .set(ACTUALIZADO_EN, java.time.LocalDateTime.now())
                .where(CAEA.eq(caeaCode))
                .execute()

            if (updated > 0) {
                logger.info("CAEA $caeaCode marcado como informado ($tipoInforme) y estado actualizado a INFORMADO")
            } else {
                logger.warn("No se pudo marcar CAEA $caeaCode como informado")
            }

            updated
        }
    }

    override fun isInformado(caeaCode: String): Boolean {
        return Database.dsl.transactionResult { config ->
            val dsl = DSL.using(config)

            val informado = dsl.select(DSL.field("informado", Boolean::class.java))
                .from(CAEA_CODES)
                .where(CAEA.eq(caeaCode))
                .fetchOne()
                ?.getValue("informado", Boolean::class.java)

            informado ?: false
        }
    }

    override fun findByPuntoVentaPeriodoOrden(puntoVenta: Int, periodo: String, orden: Int): CaeaCode? {
        return Database.dsl.transactionResult { config ->
            val dsl = DSL.using(config)

            val record = dsl.select()
                .from(CAEA_CODES)
                .where(PUNTO_VENTA.eq(puntoVenta))
                .and(PERIODO.eq(periodo))
                .and(ORDEN.eq(orden))
                .fetchOne()

            record?.let { mapToCaeaCode(it) }
        }
    }

    private fun mapToCaeaCode(record: Record): CaeaCode {
        return CaeaCode(
            id = record.getValue(ID),
            caea = record.getValue(CAEA),
            puntoVenta = record.getValue(PUNTO_VENTA),
            periodo = record.getValue(PERIODO),
            fechaDesde = convertToLocalDate(record.getValue(FECHA_DESDE)),
            fechaHasta = convertToLocalDate(record.getValue(FECHA_HASTA)),
            orden = record.getValue(ORDEN),
            estado = EstadoCaea.valueOf(record.getValue(ESTADO)),
            ultimoNumeroFacturaB = record.getValue(ULTIMO_NUMERO_FACTURA_B),
            ultimoNumeroNotaCreditoB = record.getValue(ULTIMO_NUMERO_NOTA_CREDITO_B),
            ultimoNumeroNotaDebitoB = record.getValue(ULTIMO_NUMERO_NOTA_DEBITO_B),
            creadoEn = convertToLocalDateTime(record.getValue(CREADO_EN)),
            actualizadoEn = convertToLocalDateTime(record.getValue(ACTUALIZADO_EN))
        )
    }

    /**
     * Convierte java.sql.Date a java.time.LocalDate
     */
    private fun convertToLocalDate(sqlDate: Any?): LocalDate {
        return when (sqlDate) {
            is java.sql.Date -> sqlDate.toLocalDate()
            is java.time.LocalDate -> sqlDate
            is java.sql.Timestamp -> sqlDate.toLocalDateTime().toLocalDate()
            null -> LocalDate.now()
            else -> {
                logger.warn("Tipo de fecha no reconocido: ${sqlDate::class.java}, usando fecha actual")
                LocalDate.now()
            }
        }
    }

    /**
     * Convierte java.sql.Timestamp a java.time.LocalDateTime
     */
    private fun convertToLocalDateTime(sqlTimestamp: Any?): LocalDateTime {
        return when (sqlTimestamp) {
            is java.sql.Timestamp -> sqlTimestamp.toLocalDateTime()
            is java.time.LocalDateTime -> sqlTimestamp
            is java.sql.Date -> sqlTimestamp.toLocalDate().atStartOfDay()
            null -> LocalDateTime.now()
            else -> {
                logger.warn("Tipo de timestamp no reconocido: ${sqlTimestamp::class.java}, usando timestamp actual")
                LocalDateTime.now()
            }
        }
    }
}
